<template>
    <div class="card mt-5">
        <div class="card-body">
            <div class="d-flex col-4 align-items-center ">
                <div class="position-relative w-md-300px me-md-2">
                    <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="currentColor"></path>
                        </svg>
                    </span>
                    <input v-model="searchQuery" @keyup.enter="onSearch" class="form-control form-control-solid ps-10"
                        type="text" placeholder="Search Courses" autocomplete="off" />
                </div>
            </div>
        </div>
    </div>
    <div class="heading">
        <p class=" fs-2 my-5">{{ paginatedCourses.length }} Courses Found</p>
    </div>
    <div v-if="paginatedCourses.length > 0" class="row row-cols-1 row-cols-md-4 g-4">
        <div v-for="course in paginatedCourses" :key="course.id" class="col">
            <div class="card ">
                <div class="card-body bg-light">
                    <div class="placeholder-glow">
                        <img :src="course.tileimage_fullpath" alt="Course Image"
                            style="width: 302px; height: 302px; object-fit: cover; border-radius: 5px; background-color: #e0e0e0;" />
                    </div>
                    <h4 class="card-title mt-3">

                        {{ course.title }}</h4>
                    <h4 class="card-text text-dark ">
                        <i :class="['content-type-icon', course.icon_class]"></i>
                        {{ course.module_type }}
                    </h4>
                </div>
            </div>
        </div>
    </div>
    <div v-else-if="!loading" class="card align-items-center p-5 bg-white rounded">
        <div class="card-header border-0 justify-content-center">
            <div class="card-title align-items-center flex-column ">
                <p>Looks like you don't have any courses yet.</p>
                <p>To add a course either click <a href="#">'Add Course'</a> or <a href="#">'Contact Us'</a> for more
                    information.</p>
            </div>
        </div>
        <div class="card-body align-items-center">
            <button type="button" class="btn btn-light  px-18 py-4 " data-kt-menu-trigger="click"
                data-kt-menu-placement="bottom-end">
                Contact Us
            </button>
        </div>
    </div>
    <div class="d-flex justify-content-end py-10" v-if="!loading && pagination.total > 0">
        <TablePagination :total-pages="pagination.last_page" :total="pagination.total" :per-page="pagination.per_page"
            :current-page="pagination.current_page" @page-change="onPageChange" />
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';
import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
import ApiService from "@/core/services/ApiService";
import axios from 'axios';

export default defineComponent({
    components: {
        TablePagination,
    },
    setup() {
        const loading = ref(false);
        const searchQuery = ref('');
        const debouncedSearch = ref('');
        let debounceTimeout: any = null;

        const pagination = ref({
            current_page: 1,
            per_page: 4,
            last_page: 1,
            total: 0,
            to: 0,
            from: 0,
        });

        const paginatedCourses = ref<any[]>([]);

        const fetchCompanyModules = async (page = 1) => {
            loading.value = true;
            try {
                const params = new URLSearchParams();
                if (debouncedSearch.value) params.append('search', debouncedSearch.value);
                params.append('per_page', pagination.value.per_page.toString());
                params.append('page', page.toString());

                const response = await axios.get(`employer/get-company-IndustryUnits?${params.toString()}`);

                const data = response.data.data
                    ? Array.isArray(response.data.data)
                        ? response.data.data
                        : Object.values(response.data.data)
                    : [];

                const meta = response.data.meta;

                paginatedCourses.value = data.map((item: any) => ({
                    ...item
                }));
                pagination.value.current_page = meta.current_page || 1;
                pagination.value.last_page = meta.last_page || 1;
                pagination.value.total = meta.total || data.length;
                pagination.value.per_page = meta.per_page || pagination.value.per_page;
                pagination.value.from = meta.from || 0;
                pagination.value.to = meta.to || 0;

            } catch (error) {
                console.error('Error fetching Industry Units:', error);
                paginatedCourses.value = [];
                pagination.value = { current_page: 1, per_page: pagination.value.per_page, last_page: 1, total: 0, from: 0, to: 0 };
            } finally {
                loading.value = false;
            }
        };

        const onPageChange = (page: number) => {
            pagination.value.current_page = page;
            fetchCompanyModules(page);
        };

        const applyFilter = () => {
            pagination.value.current_page = 1;
            fetchCompanyModules(1);
        };

        const isCoursesEmpty = computed(() => {
            return paginatedCourses.value.length === 0 && !loading.value;
        });

        watch(searchQuery, (newVal) => {
            if (debounceTimeout) clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(() => {
                debouncedSearch.value = newVal;
                applyFilter();
            }, 300);
        });

        onMounted(() => {
            fetchCompanyModules();
        });

        return {
            loading,
            searchQuery,
            paginatedCourses,
            pagination,
            isCoursesEmpty,
            applyFilter,
            onPageChange,
        };
    },
});
</script>

<style scoped>
.placeholder {
    height: 302px;
    width: 302px;
    display: block;
}

/* .card-title {
  width: 302px;
} */

.content-type-icon {
    font-size: 26px !important;
    color: #000 !important;
    vertical-align: middle !important;
}
</style>
