<template>
    <div id="kt_project_users_table_pane" class="tab-pane fade">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="table-responsive">
                    <table id="kt_project_users_table"
                        class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold">
                        <thead class="fs-7 text-gray-400 text-uppercase">
                            <tr>
                                <th class="min-w-150px">Course Name</th>
                                <th class="min-w-90px">Type</th>
                                <th class="min-w-90px">Status</th>
                                <th class="min-w-90px">Credentials</th>
                                <th class="min-w-50px">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="fs-6">
                            <tr v-for="course in courses" :key="course.id">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-5 position-relative">
                                            <img :src="course.tileimage_fullpath" alt="Course Image"
                                                class="img-thumbnail rounded"
                                                style="width: 50px; height: 50px; object-fit: cover; background-color: #e0e0e0;" />
                                        </div>
                                        <div class="d-flex flex-column justify-content-center">
                                            <div class="mb-1 text-gray-800 text-hover-primary">{{ course.title }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span>{{ course.courseType }}</span>
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" />
                                    </div>
                                </td>
                                <td>
                                    <span title="Badge" class="me-2"
                                        :class="{ 'text-muted': !course.badge_id, 'disabled': !course.badge_id }"
                                        :style="{ cursor: course.badge_id ? 'pointer' : 'not-allowed' }"
                                        @click="course.badge_id && openBadgeModal(course)">
                                        <i class="fas fa-award"></i>
                                    </span>
                                    <span v-if="course.courseType !== 'Lessons'" title="viewCertificate">
                                        <a href="#" @click.prevent="viewCertificate(course)">
                                            <i class="fas fa-certificate"></i>
                                        </a>
                                    </span>
                                </td>
                                <td >

                                    <a :href="getModuleLink(course)" target="_blank"
                                        class="text-primary  text-hover-underline">
                                        View Module
                                    </a>
                                    <!-- <button
                                        class="btn btn-sm btn-secondary px-5 pt-2 pb-2 rounded-2 d-flex align-items-center gap-2"
                                        data-kt-menu-trigger="click"
                                        data-kt-menu-placement="bottom-end"
                                        data-kt-menu-flip="top-end"
                                    >
                                        Action
                                        <i class="fa-solid fa-angle-down"></i>
                                        
                                    </button>
                                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fs-7 w-175px py-4" data-kt-menu="true">
                                        <div class="menu-item px-3">
                                            <a :href="`/#/courses/insights/${course.id}?type=${course.courseType}`" target="_blank" class="menu-link px-3">
                                                View Insights
                                            </a>
                                        </div>
                                        <div class="menu-item px-3">
                                            <a href="#" target="_blank" class="menu-link px-3">
                                                View Module
                                            </a>
                                        </div>
                                    </div> -->

                                    <!-- <td class="d-flex align-items-center">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-light px-5 pt-3 pb-2 rounded-0 dropdown-toggle"
                                            type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                                            aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                            <li><a class="dropdown-item" :href="`/#/courses/insights/${course.id}?type=${course.courseType}`">View Insights</a>
                                            </li>
                                            <li><a class="dropdown-item" href="#">View Module</a></li>
                                        </ul>
                                    </div>
                                </td> -->

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <EmployerBadgeModal :selectedBadge="selectedBadge" />
    <div class="modal fade" id="kt_modal_viewCertificate" tabindex="-1" aria-hidden="true">
        <div :class="['modal-dialog modal-dialog-centered', isFullscreen ? 'custom-fullscreen-modal' : 'mw-1200px']">
            <div class="modal-content rounded-0 mt-5">
                <div class="modal-header py-3">
                    <h5 class="modal-title">Certificate Preview</h5>
                    <div>
                        <span class="mx-4 cursor-pointer" @click="toggleFullscreen">
                            <i v-if="isFullscreen" class="fa-solid fa-compress text-black"></i>
                            <i v-else class="fa-solid fa-expand text-black"></i>
                        </span>
                        <!-- <button type="button" class="btn me-2" @click="downloadCertificatePdf">
                            <i class="fa fa-download"></i> 
                        </button> -->
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body p-0 text-white text-center">
                    <iframe v-if="certificateUrl" :src="certificateUrl" class="w-100"
                        :style="{ height: isFullscreen ? '90vh' : '80vh', border: 'none' }" allowfullscreen></iframe>
                    <p v-else>Loading...</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, nextTick } from 'vue';
import * as bootstrap from 'bootstrap';
import EmployerBadgeModal from './EmployerBadgeModal.vue';
import { MenuComponent } from "@/assets/ts/components";
import axios from 'axios';


export interface Course {
    id: number;
    title?: string;
    tileimage_fullpath?: string;
    courseType?: string;
    module_type?: string;
    level?: string;
    atar?: string;
    badge_id?: any;
    credential_id?: string;
    issue_date?: string;
    expiration_date?: string;
}

export default defineComponent({
    name: 'CoursesListView',
    components: { EmployerBadgeModal },
    props: {
        courses: {
            type: Array as () => Course[],
            required: true,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    mounted(props) {
        // Initialize Metronic menu components after component is mounted
        nextTick(() => {
            MenuComponent.createInstances('[data-kt-menu="true"]');
        });
    },
    updated() {
        // Re-initialize menu components when data updates
        nextTick(() => {
            MenuComponent.createInstances('[data-kt-menu="true"]');
        });
    },

    setup() {
        const selectedBadge = ref<any | null>(null);
        const certificateUrl = ref('');
        const dummyCertificateUrl = ref('/certificate-download/9028?preview=true');
        const isFullscreen = ref(false);
        const openBadgeModal = async (course: any) => {
            if (course.badge_id) {
                try {
                    const response = await axios.get(`/badge/${course.badge_id}`);
                    const badge = response.data;

                    selectedBadge.value = {
                        module_name: course.title,
                        module_type: course.courseType || '',
                        credential_id: course.credential_id || '',
                        issue_date: course.issue_date || '',
                        expiration_date: course.expiration_date || '',
                        badge: {
                            id: badge.id,
                            name: badge.name,
                            image_fullpath: badge.image_fullpath,
                            animated_image_fullpath: badge.animated_image_fullpath,
                            companies: badge.companies || [],
                            video: badge.video || null,
                        },
                    };

                    // Show modal using Bootstrap API
                    setTimeout(() => {
                        const modalElement = document.getElementById('kt_modal_badge');
                        if (modalElement) {
                            let bsModal = bootstrap.Modal.getInstance(modalElement);
                            if (!bsModal) {
                                bsModal = new bootstrap.Modal(modalElement, { backdrop: 'static' });
                            }
                            bsModal.show();
                        }
                    }, 0);
                } catch (error) {
                    console.error('Error fetching badge details:', error);
                }
            }
        };


        // Helper to determine if a course is a Skills Training module
        function isSkillsTraining(course: any) {
            return (
                course.courseType === 'Skills Training' ||
                course.module_type === 'Skills Training'
            );
        }
        // Unified viewCertificate method for both types
        function viewCertificate(course: any) {
            const templateId = course.template_id || course.id;
            const type = course.courseType === 'Skills Training' || course.module_type === 'Skills Training' ? 'skillstraining' : 'workexperience';
            const title = encodeURIComponent(course.title || '');
            certificateUrl.value = `/certificate-preview/${templateId}/${type}?title=${title}`;
            // Show the modal using Bootstrap
            setTimeout(() => {
                const modalElement = document.getElementById('kt_modal_viewCertificate');
                if (modalElement) {
                    let bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (!bsModal) {
                        bsModal = new bootstrap.Modal(modalElement, { backdrop: 'static' });
                    }
                    bsModal.show();
                }
            }, 0);
        }
        function toggleFullscreen() {
            isFullscreen.value = !isFullscreen.value;
        }
        function downloadCertificatePdf() {
            if (!certificateUrl.value) return;
            // Open the PDF in a new tab for download (assumes the backend returns a PDF)
            const downloadUrl = certificateUrl.value + (certificateUrl.value.includes('?') ? '&' : '?') + 'download=1';
            window.open(downloadUrl, '_blank');
        }


        function getModuleLink(course: Course): string {

            let moduletype;
            if (course.module_type == "Lessons") {
                moduletype = "lessons";
            } else if (course.module_type == "Virtual Work Experience") {
                moduletype = "vwe";
            } else {
                moduletype = "skillstraining";
            }
            return `/#/tasks/${moduletype}/${course.id}`;
          

            // Example: return a route path based on course type
            // return `/#/task/${moduletype}/${course.id}`;
        }
        
        return { 
            selectedBadge,
            getModuleLink,
            openBadgeModal,
            isSkillsTraining,
            certificateUrl,
            dummyCertificateUrl,
            viewCertificate,
            isFullscreen,
            toggleFullscreen,
            downloadCertificatePdf 
        };
    }
    
});
</script>

<style scoped>
.custom-fullscreen-modal {
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0;
}

.mw-1200px {
    max-width: 1200px;
}
</style>