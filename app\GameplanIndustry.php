<?php

namespace App;

use App\Services\EmployerPipelineService;
use Illuminate\Database\Eloquent\Model;

class GameplanIndustry extends Model
{
    protected $fillable = ['gameplan_id', 'industry_category_id', 'priority'];

    protected static function booted()
    {
        // Clear pipeline cache when gameplan industries change
        $clearCaches = function ($gameplanIndustry) {
            app(EmployerPipelineService::class)->clearCacheForGameplanIndustry($gameplanIndustry);

            // Clear industries chart cache for all companies
            \Cache::forget('industries_chart_data_*');

            // Clear specific company caches if we can determine which companies are affected
            $gameplan = $gameplanIndustry->gameplan;
            if ($gameplan && $gameplan->user_id) {
                // Get all companies that have this industry
                $industryId = $gameplanIndustry->industry_category_id;
                $companyIds = \DB::table('company_industry_category')
                    ->where('industry_category_id', $industryId)
                    ->pluck('company_id');

                foreach ($companyIds as $companyId) {
                    \Cache::forget("industries_chart_data_{$companyId}");
                }
            }
        };

        static::created($clearCaches);
        static::updated($clearCaches);
        static::deleted($clearCaches);
    }

    // Relationships
    public function gameplan()
    {
        return $this->belongsTo(Gameplan::class);
    }

    public function industryCategory()
    {
        return $this->belongsTo(IndustryCategory::class, 'industry_category_id');
    }
}
