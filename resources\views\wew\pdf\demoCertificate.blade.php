<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
        @page {
            margin: 0px !important;
            size: 794px 1123px;
            /* for 96 PPI */
        }

        @font-face {
            font-family: 'PT Sans';
            src: url('fonts/PT_Sans-Regular.ttf');
            font-style: normal;
            font-weight: normal;
        }

        @font-face {
            font-family: 'Roboto';
            src: url(../fonts/Roboto-Regular.ttf);
            font-style: normal;
            font-weight: normal;
        }

        .text-center {
            text-align: center !important;
        }

        body {
            /*font-family: 'Source Sans Pro';*/
            border: 5px solid #0A0AFD;
            margin: 0px !important;
            padding: 100px 0 0;
            margin-bottom: -10px !important;
            letter-spacing: 0.6px;
            background-color: #fff;
            padding: 3px;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
            font-family: 'Roboto' !important;
            line-height: 14px;
        }


        .logo {
            width: 500px;
            margin: 0px auto !important;
        }

        .dynamic-image {
            width: 70%;
            margin: 20px auto !important;
            padding: 10px;
        }

        .logo-cross {
            width: 140px;
            margin: 20px auto !important;
            padding: 10px;
        }

        .logo>img,
        .logo-cross>img,
        .dynamic-image>img {
            width: 100%;
        }

        .date {
            text-align: center;
        }

        .name {
            margin: 0;
            padding: 0;
            text-align: center;
            font-size: 18px;
            font-family: 'Roboto', sans-serif !important;
        }

        .description {
            text-align: center !important;
            font-size: 14px;
            font-weight: normal;
            margin-bottom: 30px;
        }

        .response-box {
            padding: 20px 40px;
            width: 60%;
            margin: 60px auto 20px;
            /* background-color: #eee; */
        }


        .feedback-heading {
            font-family: 'Roboto', sans-serif !important;
            font-size: 14px;
            margin-bottom: 0px;
        }

        .feedback * {
            font-family: 'Roboto', sans-serif !important;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            width: 100%;
            /* position: absolute; */
            bottom: 20;
        }

        .footer p {
            padding: 0 20px;
            font-size: 11px;
            line-height: 15px !important;
            margin-bottom: 10px;
            font-family: 'Roboto', sans-serif !important;
        }

        .footer .bottom {
            text-align: center;
        }
    </style>

</head>

<body class="full-width text-black">
    <div class="header-section">
        <div class="logo text-center">
            @if (url('/') == 'http://school.test')
            <img src="https://app.thecareersdepartment.com/css/img/logo-register.png" alt="logo">
            @else
            <img src="{{ asset('/css/img/logo-register.png') }}" alt="logo">
            @endif
        </div>
        @if ($template?->certificate_image && Storage::exists($template?->certificate_image))
        <div class="dynamic-image">
            <img src="{{ Storage::url($template?->certificate_image) }}" alt="logo">
        </div>
        @else
        <div class="logo-cross text-center">
            @if (url('/') == 'http://school.test')
            <img src="https://app.thecareersdepartment.com/images/blue-cross-red-bordered.png" alt="*">
            @else
            <img src="{{ asset('/images/blue-cross-red-bordered.png') }}" alt="*">
            @endif
        </div>
        @endif

        <h3 class="name">[ Student Name ]</h3>
        <p class="description">has completed a {{ $type }}<br />
            through The Careers Department.</p>
        <h3 class="name">{{ $template?->title }}</h3>
        <p class="date">[ DATE ]</p>
    </div><br />

    {{-- @if ($response->feedback) --}}
    <div class="response-box">
        {{-- <h3 class="feedback-heading">VIRTUAL WORK EXPERIENCE FEEDBACK</h3> --}}
        {{-- <div class="feedback"> {!! $response->feedback !!} </div> --}}
    </div>
    {{-- @endif --}}

    @if ($type === 'skills workshop')
    <div class="footer">
        <p>
            Find out more information about this program at <a
                href="https://www.thecareersdepartment.com/">www.careersdepartment.com</a><br />© The Footnotes Pty Ltd
        </p>
    </div>
    @elseif ($type === 'virtual work experience')
    <div class="footer">
        <p>Completing Virtual Work experience through The Careers Department means that the recipient of this
            certificate, along with an infinite number of other students, has completed a hypothetical task, it does not
            mean they were employed by The Careers Department or the company that has created the work experience task.
            Find out more information about this program at<a
                href="https://www.thecareersdepartment.com/">www.careersdepartment.com</a> ©The Footnotes Pty Ltd
        </p>
    </div>
    @endif
</body>