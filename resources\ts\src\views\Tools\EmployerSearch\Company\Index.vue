<template>
    <div class="position-relative">
        <!-- Background Image -->
        <img src="https://picsum.photos/1200/400" class="w-100" style="height: 40vh; object-fit: cover;" />

        <!-- Card Overlapping -->
        <div class="card p-4 mx-auto position-relative z-1 mb-xxl-8" style=" margin-top: -230px; max-width: 95%;">
            <div class="card-body pt-9 ms-xl-10 pb-0">
                <!--begin::Details-->
                <!-- 
                 d-flex flex-wrap flex-sm-nowrap
                 me-7 mb-4
                 flex-grow-1
                -->
                <div class="row">
                    <!--begin: Pic-->
                    <div class="col-12 col-xl-2">
                        <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
                            <img src="https://picsum.photos/1200/400" alt="image">

                        </div>
                    </div>
                    <!--end::Pic-->

                    <!--begin::Info-->
                    <div class="col-12 mt-0 mt-md-5 col-xl-10">
                        <!--begin::Title-->
                        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                            <!--begin::User-->
                            <div class="d-flex flex-column">
                                <!--begin::Name-->

                                <div class="d-flex align-items-center mb-2">
                                    <a href="#" class="text-gray-900 text-hover-primary fs-1 fw-bold me-1">Max
                                        Smith</a>
                                    <a href="#"><i class="ki-outline ki-verify fs-1 text-primary"></i></a>
                                </div>
                                <!--end::Name-->

                                <!--begin::Info-->
                                <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                    <a href="#"
                                        class="d-flex align-items-center text-gray-700 text-hover-primary me-5 mb-2">
                                        <svg class="me-2" width="14" height="17" viewBox="0 0 12 17" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6 16C6 16 12 10.314 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 2.37122e-08 4.4087 0 6C0 10.314 6 16 6 16ZM6 9C5.20435 9 4.44129 8.68393 3.87868 8.12132C3.31607 7.55871 3 6.79565 3 6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3C6.79565 3 7.55871 3.31607 8.12132 3.87868C8.68393 4.44129 9 5.20435 9 6C9 6.79565 8.68393 7.55871 8.12132 8.12132C7.55871 8.68393 6.79565 9 6 9Z"
                                                fill="#606060" />
                                        </svg>
                                        Sydney

                                    </a>
                                    <a href="#"
                                        class="d-flex align-items-center text-gray-700 text-hover-primary me-5 mb-2 ">
                                    <svg class="me-2" width="16" height="17" viewBox="0 0 16 17" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_1860_2126)">
                                    <path
                                        d="M16 8.5C16 10.6217 15.1571 12.6566 13.6569 14.1569C12.1566 15.6571 10.1217 16.5 8 16.5C5.87827 16.5 3.84344 15.6571 2.34315 14.1569C0.842855 12.6566 0 10.6217 0 8.5C0 6.37827 0.842855 4.34344 2.34315 2.84315C3.84344 1.34285 5.87827 0.5 8 0.5C10.1217 0.5 12.1566 1.34285 13.6569 2.84315C15.1571 4.34344 16 6.37827 16 8.5ZM8 4C8 3.86739 7.94732 3.74021 7.85355 3.64645C7.75979 3.55268 7.63261 3.5 7.5 3.5C7.36739 3.5 7.24021 3.55268 7.14645 3.64645C7.05268 3.74021 7 3.86739 7 4V9.5C7.00003 9.58813 7.02335 9.67469 7.06761 9.75091C7.11186 9.82712 7.17547 9.89029 7.252 9.934L10.752 11.934C10.8669 11.9961 11.0014 12.0108 11.127 11.9749C11.2525 11.9391 11.3591 11.8556 11.4238 11.7422C11.4886 11.6288 11.5065 11.4946 11.4736 11.3683C11.4408 11.2419 11.3598 11.1334 11.248 11.066L8 9.21V4Z"
                                        fill="#606060" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_1860_2126">
                                        <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                                    </clipPath>
                                </defs>
                            </svg>Melbourne
                                    </a>

                                </div>
                                <!--end::Info-->
                            </div>
                            <!--end::User-->
                            <div class="gap-2 d-flex flex-wrap">
                                <button
                                    class="flex items-center gap-2 py-1 bg-gray-50 text-gray-700 border-0 px-3  rounded  fs-6 hover:bg-gray-100 transition">
                                    <span>LinkedIn</span>
                                    <svg width="20" height="20" viewBox="0 0 25 25" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M6.25 12.499C6.25 12.2918 6.33231 12.0931 6.47882 11.9466C6.62534 11.8001 6.82405 11.7178 7.03125 11.7178H16.0828L12.7281 8.36466C12.5814 8.21796 12.499 8.019 12.499 7.81153C12.499 7.60407 12.5814 7.40511 12.7281 7.25841C12.8748 7.11171 13.0738 7.0293 13.2812 7.0293C13.4887 7.0293 13.6877 7.11171 13.8344 7.25841L18.5219 11.9459C18.5946 12.0185 18.6524 12.1047 18.6917 12.1996C18.7311 12.2945 18.7514 12.3963 18.7514 12.499C18.7514 12.6018 18.7311 12.7035 18.6917 12.7985C18.6524 12.8934 18.5946 12.9796 18.5219 13.0522L13.8344 17.7397C13.6877 17.8864 13.4887 17.9688 13.2812 17.9688C13.0738 17.9688 12.8748 17.8864 12.7281 17.7397C12.5814 17.593 12.499 17.394 12.499 17.1865C12.499 16.9791 12.5814 16.7801 12.7281 16.6334L16.0828 13.2803H7.03125C6.82405 13.2803 6.62534 13.198 6.47882 13.0515C6.33231 12.9049 6.25 12.7062 6.25 12.499Z"
                                            fill="#606060" />
                                    </svg>

                                </button>

                                <button
                                    class="flex items-center gap-2 py-1 bg-gray-50 text-gray-700 border-0  px-3 rounded  fs-6 hover:bg-gray-100 transition">
                                    <span>Instagram</span>
                                    <svg width="20" height="20" viewBox="0 0 25 25" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M6.25 12.499C6.25 12.2918 6.33231 12.0931 6.47882 11.9466C6.62534 11.8001 6.82405 11.7178 7.03125 11.7178H16.0828L12.7281 8.36466C12.5814 8.21796 12.499 8.019 12.499 7.81153C12.499 7.60407 12.5814 7.40511 12.7281 7.25841C12.8748 7.11171 13.0738 7.0293 13.2812 7.0293C13.4887 7.0293 13.6877 7.11171 13.8344 7.25841L18.5219 11.9459C18.5946 12.0185 18.6524 12.1047 18.6917 12.1996C18.7311 12.2945 18.7514 12.3963 18.7514 12.499C18.7514 12.6018 18.7311 12.7035 18.6917 12.7985C18.6524 12.8934 18.5946 12.9796 18.5219 13.0522L13.8344 17.7397C13.6877 17.8864 13.4887 17.9688 13.2812 17.9688C13.0738 17.9688 12.8748 17.8864 12.7281 17.7397C12.5814 17.593 12.499 17.394 12.499 17.1865C12.499 16.9791 12.5814 16.7801 12.7281 16.6334L16.0828 13.2803H7.03125C6.82405 13.2803 6.62534 13.198 6.47882 13.0515C6.33231 12.9049 6.25 12.7062 6.25 12.499Z"
                                            fill="#606060" />
                                    </svg>

                                </button>


                                <button
                                    class="flex items-center gap-2 py-1 bg-gray-50 text-gray-700 border-0  rounded px-3  fs-6 hover:bg-gray-100 transition">
                                    <span>Website</span>
                                    <svg width="20" height="20" viewBox="0 0 25 25" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M6.25 12.499C6.25 12.2918 6.33231 12.0931 6.47882 11.9466C6.62534 11.8001 6.82405 11.7178 7.03125 11.7178H16.0828L12.7281 8.36466C12.5814 8.21796 12.499 8.019 12.499 7.81153C12.499 7.60407 12.5814 7.40511 12.7281 7.25841C12.8748 7.11171 13.0738 7.0293 13.2812 7.0293C13.4887 7.0293 13.6877 7.11171 13.8344 7.25841L18.5219 11.9459C18.5946 12.0185 18.6524 12.1047 18.6917 12.1996C18.7311 12.2945 18.7514 12.3963 18.7514 12.499C18.7514 12.6018 18.7311 12.7035 18.6917 12.7985C18.6524 12.8934 18.5946 12.9796 18.5219 13.0522L13.8344 17.7397C13.6877 17.8864 13.4887 17.9688 13.2812 17.9688C13.0738 17.9688 12.8748 17.8864 12.7281 17.7397C12.5814 17.593 12.499 17.394 12.499 17.1865C12.499 16.9791 12.5814 16.7801 12.7281 16.6334L16.0828 13.2803H7.03125C6.82405 13.2803 6.62534 13.198 6.47882 13.0515C6.33231 12.9049 6.25 12.7062 6.25 12.499Z"
                                            fill="#606060" />
                                    </svg>

                                </button>

                                <button
                                    class="flex items-center gap-2 py-1 bg-gray-50 text-gray-700 border-0  rounded px-3 fs-6 hover:bg-gray-100 transition">
                                    <span>Custom</span>
                                    <svg width="20" height="20" viewBox="0 0 25 25" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M6.25 12.499C6.25 12.2918 6.33231 12.0931 6.47882 11.9466C6.62534 11.8001 6.82405 11.7178 7.03125 11.7178H16.0828L12.7281 8.36466C12.5814 8.21796 12.499 8.019 12.499 7.81153C12.499 7.60407 12.5814 7.40511 12.7281 7.25841C12.8748 7.11171 13.0738 7.0293 13.2812 7.0293C13.4887 7.0293 13.6877 7.11171 13.8344 7.25841L18.5219 11.9459C18.5946 12.0185 18.6524 12.1047 18.6917 12.1996C18.7311 12.2945 18.7514 12.3963 18.7514 12.499C18.7514 12.6018 18.7311 12.7035 18.6917 12.7985C18.6524 12.8934 18.5946 12.9796 18.5219 13.0522L13.8344 17.7397C13.6877 17.8864 13.4887 17.9688 13.2812 17.9688C13.0738 17.9688 12.8748 17.8864 12.7281 17.7397C12.5814 17.593 12.499 17.394 12.499 17.1865C12.499 16.9791 12.5814 16.7801 12.7281 16.6334L16.0828 13.2803H7.03125C6.82405 13.2803 6.62534 13.198 6.47882 13.0515C6.33231 12.9049 6.25 12.7062 6.25 12.499Z"
                                            fill="#606060" />
                                    </svg>

                                </button>
                            </div>


                        </div>
                        <!--end::Title-->

                        <!--begin::Stats-->
                        <div class="d-flex flex-wrap flex-stack">
                            <!--begin::Wrapper-->
                            <div class=" fs-5 text-gray-700 pe-8">
                                Nature <svg class="m-2" width="3" height="3" viewBox="0 0 3 3" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="1.5" cy="1.5" r="1.5" fill="#606060" />
                                </svg>
                                Environment and Agriculture <svg class="m-2" width="3" height="3" viewBox="0 0 3 3"
                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="1.5" cy="1.5" r="1.5" fill="#606060" />
                                </svg>
                                Science <svg class="m-2" width="3" height="3" viewBox="0 0 3 3" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="1.5" cy="1.5" r="1.5" fill="#606060" />
                                </svg>
                                Engineering and Math

                            </div>
                            <!--end::Wrapper-->

                            <!--begin::Progress-->

                            <!--end::Progress-->
                        </div>
                        <!--end::Stats-->
                    </div>
                    <!--end::Info-->
                </div>
                <!--end::Details-->
            </div>
            <div class="row mt-5">
                <div class="col-md-5 col-lg-2 ms-xl-18">
                    <div class="card">
                        <div class="card-body p-0">
                            <ul class="nav nav-tabs nav-pills flex-row border-0 flex-md-column me-xl-5 mb-3 mb-md-0 fs-6"
                                id="v-pills-tab" role="tablist" aria-orientation="vertical">

                                <li class="nav-item w-100 me-0 mb-md-2">
                                    <button
                                        class="nav-link py-5  w-75 btn btn-flex btn-active-light-secondary btn-active-color-dark"
                                        id="v-pills-about-tab" data-bs-toggle="pill" data-bs-target="#v-pills-about"
                                        type="button" role="tab" :class="{ active: activeTab === 'about' }"
                                        @click="activeTab = 'about'">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">About</span>
                                        </span>
                                    </button>
                                </li>

                                <li class="nav-item w-100 me-0 mb-md-2">
                                    <button
                                        class="nav-link py-5  w-75 btn btn-flex btn-active-light-secondary btn-active-color-dark"
                                        id="v-pills-jobs-tab" data-bs-toggle="pill" data-bs-target="#v-pills-jobs"
                                        type="button" role="tab" :class="{ active: activeTab === 'jobs' }"
                                        @click="activeTab = 'jobs'">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Jobs</span>
                                        </span>
                                    </button>
                                </li>

                                <li class="nav-item w-100 me-0 mb-md-2">
                                    <button
                                        class="nav-link py-5  w-75 btn btn-flex btn-active-light-secondary btn-active-color-dark"
                                        id="v-pills-like-tab" data-bs-toggle="pill" data-bs-target="#v-pills-like"
                                        type="button" role="tab">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Life at Rio Tinto</span>
                                        </span>
                                    </button>
                                </li>

                                <li class="nav-item w-100 me-0 mb-md-2">
                                    <button
                                        class="nav-link py-5  w-75 btn btn-flex btn-active-light-secondary btn-active-color-dark"
                                        id="v-pills-linked-tab" data-bs-toggle="pill" data-bs-target="#v-pills-linked"
                                        type="button" role="tab">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Linked</span>
                                        </span>
                                    </button>
                                </li>

                                <li class="nav-item w-100 me-0">
                                    <button
                                        class="nav-link py-5  w-75 btn btn-flex btn-active-light-secondary btn-active-color-dark"
                                        id="v-pills-courses-tab" data-bs-toggle="pill" data-bs-target="#v-pills-courses"
                                        type="button" role="tab">
                                        <span class="d-flex flex-column align-items-start">
                                            <span class="fs-4 fw-bold">Courses</span>
                                        </span>
                                    </button>
                                </li>
                            </ul>
                        </div>

                    </div>
                </div>
                <div class="col-md-7 col-lg-9 px-0">
                    <component :is="currentComponent" />
                </div>
            </div>
        </div>
      
    </div>
</template>

<script lang="ts">
    import { defineComponent, ref, onMounted, computed, watch } from 'vue';
    import BlockView from "@/components/employer/search/BlockView.vue";
    import ListView from "@/components/employer/search/ListView.vue";
    import MapView from "@/components/employer/search/MapView.vue";
    import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
    import ApiService from "@/core/services/ApiService";
    import About from "./About.vue";
    import Jobs from "./Jobs.vue"
    import {
        Field
    } from "vee-validate";
    import Multiselect from '@vueform/multiselect';

    interface Student {
        id?: number;
        name?: string;
        email?: string;
        location?: string | null;
        year?: string | null;
        initials?: string | null;
        industriesCount?: number;
        jobsCount?: number;
        companiesCount?: number;
    }

    interface PaginationMeta {
        current_page: number;
        from: number;
        last_page: number;
        per_page: number;
        to: number;
        total: number;
    }

    interface PaginationLinks {
        first: string;
        last: string;
        prev: string | null;
        next: string | null;
    }

    interface StudentsResponse {
        data: { data: Student[] };
        meta: PaginationMeta;
        links: PaginationLinks;
    }



    export default defineComponent({
        components: {
            BlockView,
            ListView,
            MapView,
            TablePagination,
            Field,
            Multiselect,
            About,
            Jobs,
        },


        setup() {


              const activeTab = ref("Jobs");

    const currentComponent = computed(() => {
      switch (activeTab.value) {
        case "about":
          return "About";
        case "jobs":
          return "Jobs";
        case "life":
          return "Life";
        case "linked":
          return "Linked";
        case "courses":
          return "Courses";
        case "Abc":
          return "Abc";
        default:
          return "About";
      }
    });
            const banner = ref({
                'trailer_video': null,
                'video': null,
                'imagefullpath': null,
            });
            const students = ref<Student[]>([]);
            const paginationMeta = ref<PaginationMeta>({
                current_page: 1,
                from: 0,
                last_page: 1,
                per_page: 8,
                to: 0,
                total: 0,
            });
            const paginationLinks = ref<PaginationLinks>({
                first: '',
                last: '',
                prev: null,
                next: null,
            });
            const loading = ref(false);
            const searchQuery = ref('');
            const debouncedSearch = ref('');
            let debounceTimeout: any = null;

            const currentTab = ref('all-students');
            const currentViewTab = ref('card');

            const filters = ref({
                courseType: "",
                difficulty: "",
            });
            const filteredCourses = ref<any[]>([]);

            const companyModules = ref({
                lessons: [],
                virtual_work_experiences: [],
                skills_trainings: []
            });

            const pagination = ref({
                current_page: 1,
                per_page: 8,
                last_page: 1,
                total: 0,
                to: 0,
                from: 0
            });

            const paginatedCourses = ref<any[]>([]);

            const courseTypes = [
                { value: "Virtual Work Experience", label: "Virtual Work Experience" },
                { value: "Lessons", label: "Lessons" },
                { value: "Skills Training", label: "Skills Training" },
            ];

            const difficulty = [
                { value: "Beginner", label: "Beginner" },
                { value: "Intermediate", label: "Intermediate" },
                { value: "Expert", label: "Expert" },
            ];

            // banner.value = {
            //     'trailer_video': null,
            //     'video': null,
            //     'imagefullpath': null,
            // }

            const fetchBanner = async () => {
                try {
                    const response = await fetch(
                        'api/getBanner/Employer Pipeline',
                        {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                            },
                        }
                    );

                    const data = await response.json();
                    banner.value = data;
                } catch (error) {
                    console.log(error)
                }
            };

            const fetchCompanyModules = async (page = 1) => {
                loading.value = true;
                try {
                    const params = new URLSearchParams();
                    if (filters.value.courseType) params.append('courseType', filters.value.courseType);
                    if (filters.value.difficulty) params.append('difficulty', filters.value.difficulty);
                    if (debouncedSearch.value) params.append('search', debouncedSearch.value);
                    params.append('per_page', pagination.value.per_page.toString());
                    params.append('page', page.toString());

                    const response = await ApiService.get(`employer/get-company-modules?${params.toString()}`);
                    // Backend now returns a single paginated array and meta
                    const data = response.data.data || [];
                    const meta = response.data.meta || {};
                    paginatedCourses.value = data.map((item: any) => {
                        // Try to infer courseType if not present
                        let courseType = item.module_type || item.courseType;
                        if (!courseType) {
                            if (item.hasOwnProperty('order')) courseType = 'Lessons';
                            else if (item.hasOwnProperty('template_type') || item.hasOwnProperty('workexperience_template_id')) courseType = 'Virtual Work Experience';
                            else courseType = 'Skills Training';
                        }
                        return {
                            ...item,
                            courseType,
                            difficulty: item.level || item.difficulty || '',
                        };
                    });
                    pagination.value.current_page = meta.current_page || 1;
                    pagination.value.last_page = meta.last_page || 1;
                    pagination.value.total = meta.total || paginatedCourses.value.length;
                    pagination.value.from = meta.from || ((pagination.value.total > 0) ? ((pagination.value.current_page - 1) * pagination.value.per_page + 1) : 0);
                    pagination.value.to = meta.to || ((pagination.value.total > 0) ? Math.min(pagination.value.from + pagination.value.per_page - 1, pagination.value.total) : 0);
                } catch (error) {
                    paginatedCourses.value = [];
                    pagination.value = { current_page: 1, per_page: pagination.value.per_page, last_page: 1, total: 0, from: 0, to: 0 };
                } finally {
                    loading.value = false;
                }
            };

            const onPageChange = (page: number) => {
                fetchCompanyModules(page);
            };

            const applyFilter = () => {
                pagination.value.current_page = 1;
                fetchCompanyModules(1);
            };

            const isCoursesEmpty = computed(() => {
                return paginatedCourses.value.length === 0 && !loading.value;
            });

            // When searchQuery changes, debounce and trigger applyFilter
            watch(searchQuery, (newVal) => {
                if (debounceTimeout) clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(() => {
                    debouncedSearch.value = newVal;
                    applyFilter(); // Trigger backend filter on search
                }, 300); // 300ms debounce
            });

            const scrollToSection = (sectionId: string) => {
                const section = document.getElementById(sectionId);

                if (section) {
                    section.scrollIntoView({ behavior: 'smooth' });
                }
            }

            onMounted(() => {
                fetchCompanyModules();
                fetchBanner();
            });

            return {
                banner,
                loading,
                searchQuery,
                currentTab,
                currentViewTab,
                scrollToSection,
                courseTypes,
                difficulty,
                filters,
                filteredCourses,
                isCoursesEmpty,
                applyFilter,
                paginationMeta,
                fetchCompanyModules,
                paginatedCourses,
                pagination,
                onPageChange,
                activeTab, currentComponent
            }
        },
    })
</script>

<style>
    .banner {
        background-color: #bbb;
        display: block;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        overflow: hidden;
        min-height: 30vw;
    }

    .full-view-banner {
        margin-left: -30px;
        margin-right: -30px;
    }

    .banner-video {
        height: 100%;
    }

    .banner-video>video {
        width: 101% !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }


    .nav-tabs .nav-link {
        border: none !important;
        color: #A1A5B7;
        background-color: transparent !important;
    }

    .nav-tabs .nav-link:hover {
        color: #000;
        background-color: transparent !important;
    }

    .nav-tabs .nav-link.active {
        color: #000;
        font-weight: bold;
        /* border-bottom: 2px solid #000 !important; */
        background-color: transparent !important;
        box-shadow: none !important;
    }

    .btn-white-custom {
        background: #fff;
        color: #000;
    }

    .btn-border-custom {
        border: 1px solid white !important;
        color: #000;
        background: white;
    }

    .btn-border-custom:hover {
        border: 0px !important;
        color: white;
        background: #000;
    }

    .btn-black-custom:hover,
    .btn-white-custom {
        background-color: #fff !important;
        color: #000 !important;
    }

    .btn-black-custom,
    .btn-white-custom:hover {
        background-color: #000 !important;
        color: #fff !important;
    }

    .btn-white-custom:hover,
    .btn.btn-white-custom:hover:not(.btn-active) {
        background-color: #000 !important;
        color: #fff !important;
    }

    .image img {
        height: 647px; 
    }

    @media (max-width: 1280px) {
        .banner {
            height: 95.25vw;

        }

        .banner_detail_box {
            left: 40%;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(65vw + 65vh) !important;
        }
    }


    @media (max-width: 991px) {

        .banner {
            height: 90.25vw;
        }


        .full-view-banner,
        .module-sections {
            margin-left: -20px;
            margin-right: -20px;
        }

        .full-view-banner {
            margin-top: 58.16px;
        }

    }



    @media (max-width: 991px) and (min-width: 768px) and (orientation:portrait) {
        .banner {
            height: 95.25vw;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(66vw + 66vh) !important;
        }
    }

    @media (max-width: 991px) and (orientation:landscape) {
        .banner-video>video {
            height: auto !important;
            width: calc(70vw + 70vh) !important;
        }
    }

    @media (max-width: 767px) {

        .banner {
            height: 110vw;
        }

        .banner_detail_box {
            left: 50%;
        }

    }

    @media (max-width: 575px) {
        .full-view-banner {
            margin-top: 0;
        }

        .banner {
            height: 124vw;
        }

        .banner_detail_box {
            width: 70vw !important;
        }

        .banner-video>video {
            height: 100% !important;
            width: calc(90vw + 90vh) !important;
        }
    }
</style>