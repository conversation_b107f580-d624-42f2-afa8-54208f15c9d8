<?php

namespace App\Http\Controllers\Vue;

use App\Badge;
use App\BadgeKey;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BadgesController extends Controller
{
    public function verifyCredential(Request $request)
    {
        $credentialId = $request->credential_id;
        $badgeKey = BadgeKey::where('credential_id', $credentialId)->with('badge', 'badge.companies', 'badgeable', 'badgeable.student:id,name')->first();

        if (!$badgeKey) {
            return response()->json([
                'status' => 'error',
                'message' => 'No Results Found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'badgeKey' => $badgeKey
        ]);
    }

    public function downloadBadge($id)
    {
        $badge = Badge::find($id);

        if (!$badge || !$badge->image) {
            return response()->json(['error' => 'File not found'], 404);
        }

        return Storage::cloud()->download($badge->image, $this->generateBadgeFilename($badge));
    }

    private function generateBadgeFilename($badge)
    {
        $extension = pathinfo($badge->image, PATHINFO_EXTENSION);
        $file = mb_ereg_replace("([^\w\s\d\-_~,;\[\]\(\).])", '', $badge->name);
        $file = mb_ereg_replace("([\.]{2,})", '', $file);
        $file = str_replace(" ", '_', $file);

        return $file . '.' . $extension;
    }

    public function getBadgeDetails($id)
    {
        $badge = Badge::with('companies')->find($id);

        if (!$badge) {
            return response()->json(['error' => 'Badge not found'], 404);
        }

        return response()->json($badge);
    }
}
