<template>
    <div class="card mt-5">
        <div class="card-body py-0">
            <div class="d-md-flex pt-5 align-items-center justify-content-between ">
                <div class="d-flex col-5 align-items-center ">
                    <div class="position-relative w-md-300px me-md-2">
                        <span
                            class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                    transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <input v-model="searchQuery" @keyup.enter="applyFilter"
                            class="form-control form-control-solid ps-10" type="text" placeholder="Search Courses"
                            autocomplete="off" />
                    </div>
                </div>
                <div class="d-flex col-7 flex-column flex-md-row justify-content-end align-items-center px-2">
                    <div class="mb-5 me-3 col-12 col-md-3 mt-5 mt-md-0">
                        <label class="form-label fw-semibold ms-2">Course Type</label>
                        <Field v-slot="{ field }" name="CourseType">
                            <Multiselect class="form-control form-control-solid py-2 fs-6" v-model="filters.courseType"
                                v-bind="field" :searchable="false" placeholder="Course Type" :resolve-on-load="false"
                                :options="courseTypes"></Multiselect>
                        </Field>
                    </div>
                    <div class="mb-5 me-3 col-12 col-md-2">
                        <button type="button"
                            class="btn btn-light d-flex justify-content-center align-items-center py-auto mt-8 w-100"
                            data-toggle="button" aria-pressed="false" autocomplete="off" @click="applyFilter">
                            Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="heading">
        <p class=" fs-2 my-5">{{ pagination.total }} Courses Found</p>
    </div>
    <div v-if="paginatedCourses.length > 0" class="row g-6 g-xl-9">
        <div class="col-md-6 col-xxl-3" v-for="course in paginatedCourses" :key="course.id">
            <div class="card bg-white rounded mb-5 d-flex position-relative">
                <div class="dropdown position-absolute top-0 end-0 m-2">
                    <button class="btn btn-link text-dark p-0" type="button" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i class="bi bi-three-dots fs-5"></i>
                    </button>
                    <div class="dropdown-menu menu-rounded menu-gray-600 menu-state-bg-light-primary fs-7 w-175px py-4"
                        data-kt-menu="true">
                        <div class="menu-item px-3">
                            <a :href="`/#/courses/insights/${course.id}?type=${course.courseType}`" target="_blank"
                                class="menu-link px-3">
                                View Insights
                            </a>
                        </div>
                        <div class="menu-item px-3">
                            <a href="#" target="_blank" class="menu-link px-3">
                                View Module
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class=" row justify-content-between">
                        <div class="col-5">
                            <img :src="course.tileimage_fullpath" alt="Course Image"
                                style="width: 130px; height: 128px; object-fit: cover; border-radius: 5px; background-color: #e0e0e0;" />
                        </div>
                        <div class=" col-7 ">
                            <h5 class="fw-bold mt-2">{{ course.title }}</h5>
                            <span class="badge bg-dark text-white mt-3">{{ course.courseType }}</span>
                            <div class="d-flex align-items-center mt-5">
                                <i v-if="course.level" class="bi bi-bar-chart-fill me-2"></i>
                                <span>{{ course.level }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-else-if="!loading" class="card align-items-center p-5 bg-white rounded">
        <div class="card-header border-0 justify-content-center">
            <div class="card-title align-items-center flex-column ">
                <p>Looks like you don't have any courses yet.</p>
                <p>To add a course either click <a href="#">'Add Course'</a> or <a href="#">'Contact Us'</a> for more
                    information.</p>
            </div>
        </div>
        <div class="card-body align-items-center">
            <button type="button" class="btn btn-light  px-18 py-4 " data-kt-menu-trigger="click"
                data-kt-menu-placement="bottom-end">
                Contact Us
            </button>
        </div>
    </div>
    <div class="d-flex justify-content-end py-10" v-if="!loading && pagination.total > 0">
        <TablePagination :total-pages="pagination.last_page" :total="pagination.total" :per-page="pagination.per_page"
            :current-page="pagination.current_page" @page-change="onPageChange" />
    </div>
</template>


<script lang="ts">
    import { defineComponent, ref, onMounted, computed, watch } from 'vue';
    import TablePagination from "@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue";
    import axios from 'axios';
    import {
        Field
    } from "vee-validate";
    import Multiselect from '@vueform/multiselect';

    export default defineComponent({
        components: {
            TablePagination,
            Field,
            Multiselect,
        },


        setup() {
            const banner = ref({
                'trailer_video': null,
                'video': null,
                'imagefullpath': null,
            });
            const loading = ref(false);
            const searchQuery = ref('');
            const debouncedSearch = ref('');
            let debounceTimeout: any = null;

            const filters = ref({
                courseType: "",
            });
          
            const companyModules = ref({
                lessons: [],
                virtual_work_experiences: [],
                skills_trainings: []
            });

            const pagination = ref({
                current_page: 1,
                per_page: 8,
                last_page: 1,
                total: 0,
                to: 0,
                from: 0
            });

            const paginatedCourses = ref<any[]>([]);

            const courseTypes = [
                { value: "Virtual Work Experience", label: "Virtual Work Experience" },
                { value: "Lessons", label: "Lessons" },
                { value: "Skills Training", label: "Skills Training" },
            ];

            // banner.value = {
            //     'trailer_video': null,
            //     'video': null,
            //     'imagefullpath': null,
            // }

            const fetchCompanyModules = async (page = 1) => {
                loading.value = true;
                try {
                    const params = new URLSearchParams();
                    if (filters.value.courseType) params.append('courseType', filters.value.courseType);
                    if (debouncedSearch.value) params.append('search', debouncedSearch.value);
                    params.append('per_page', pagination.value.per_page.toString());
                    params.append('page', page.toString());

                    const response = await axios.get(`employer/get-company-modules?${params.toString()}`);

                    const data = response.data.data
                        ? Array.isArray(response.data.data)
                            ? response.data.data
                            : Object.values(response.data.data)
                        : [];

                    const meta = response.data.meta;
                    paginatedCourses.value = data.map((item: any) => {
                        let courseType = item.module_type || item.courseType;
                        if (!courseType || courseType === '') {
                            if (item.hasOwnProperty('order')) courseType = 'Lessons';
                            else if (item.hasOwnProperty('template_type') || item.hasOwnProperty('workexperience_template_id')) courseType = 'Virtual Work Experience';
                            else courseType = 'Skills Training';
                        }
                        return {
                            ...item,
                            courseType
                        };
                    });
                    pagination.value.current_page = meta.current_page || 1;
                    pagination.value.last_page = meta.last_page || 1;
                    pagination.value.total = meta.total || data.length;
                    pagination.value.per_page = meta.per_page || pagination.value.per_page;
                    pagination.value.from = meta.from || 0;
                    pagination.value.to = meta.to || 0;
                } catch (error) {
                    console.error('Error fetching company modules:', error);
                    paginatedCourses.value = [];
                    pagination.value = { current_page: 1, per_page: pagination.value.per_page, last_page: 1, total: 0, from: 0, to: 0 };
                } finally {
                    loading.value = false;
                }
            };

            const onPageChange = (page: number) => {
                pagination.value.current_page = page;
                fetchCompanyModules(page);
            };

            const applyFilter = () => {
                pagination.value.current_page = 1;
                fetchCompanyModules(1);
            };

            const isCoursesEmpty = computed(() => {
                return paginatedCourses.value.length === 0 && !loading.value;
            });

            watch(searchQuery, (newVal) => {
                if (debounceTimeout) clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(() => {
                    debouncedSearch.value = newVal;
                    applyFilter();
                }, 300);
            });

            const scrollToSection = (sectionId: string) => {
                const section = document.getElementById(sectionId);

                if (section) {
                    section.scrollIntoView({ behavior: 'smooth' });
                }
            }

            onMounted(() => {
                fetchCompanyModules();
            });

            return {
                banner,
                loading,
                searchQuery,
                scrollToSection,
                courseTypes,
                filters,
                isCoursesEmpty,
                applyFilter,
                fetchCompanyModules,
                paginatedCourses,
                pagination,
                onPageChange,
            }
        },
    })
</script>


<style>
    /* .multiselect {
  
box-sizing: border-box;

} */
</style>