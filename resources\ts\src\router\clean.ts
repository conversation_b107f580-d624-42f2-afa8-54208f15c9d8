import { createRouter, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouteRecordRaw } from "vue-router";
import store from "@/store";
import { Mutations, Actions } from "@/store/enums/StoreEnums";
import JwtService from "@/core/services/JwtService";
import ApiService from "@/core/services/ApiService";

interface Element {
  msMatchesSelector(selectors: string): boolean;
}
const routes: Array<RouteRecordRaw> = [
  {
    path: "/gameplan",
    name: "gameplan",
    props: true,
    component: () => import("@/views/gameplan/MultiStepGamePlan.vue"),
    meta: {
      pageTitle: "Game Plan",
      middleware: ["auth", "hasCampus", "hasNewGamePlan"],
    },
  },
  {
    path: "/sign-up-institute/:instituteSlug",
    name: "sign-up-institute",
    props: true,
    component: () => import("@/views/crafted/authentication/institute/SignUpInstitute.vue"),
    meta: {
      pageTitle: "Sign In Institute",
    },
  },
  {
    path: "/sign-up-institute/details",
    name: "sign-up-institute-details",

    component: () => import("@/views/crafted/authentication/institute/SignUpDetails.vue"),
    meta: {
      pageTitle: "Sign Up Details",
    },
  },
  {
    path: "/",
    redirect: "/dashboard",
    component: () => import("@/layouts/main-layout/MainLayout.vue"),
    meta: {
      // middleware: "auth",
      middleware: ["auth", "hasCampus", "hasNewGamePlan"],
    },

    children: [
      {
        path: "/dashboard",
        name: "dashboard",
        component: () => import("@/views/Dashboard.vue"),
        meta: {
          pageTitle: "Dashboard",
          breadcrumbs: ["Dashboards"],
          greyBg: true,
        },
      },
      {
        path: "/profile/edit",
        name: "profile-profile",
        component: () => import("@/views/Profiles/Index.vue"),
        meta: {
          pageTitle: "Profile",
          breadcrumbs: ["Profile", "Profile Settings"],
        },
      },
      {
        path: "/courses",
        name: "courses",
        component: () => import("@/views/Courses/Index.vue"),
        meta: {
          pageTitle: "Courses",
          breadcrumbs: ["Courses", "Courses"],
        },
      },


       {
        path: "/company",
        name: "company",
        component: () => import("@/views/Company/Index.vue"),
        meta: {
          pageTitle: "company",
          breadcrumbs: ["company", "company page"],
          greyBg: true,
        },
      },

      {
        path: "/company/CompanyName/createpages",
        name: "companycreatepages",
        component: () => import("@/views/Company/CreatePage.vue"),
        meta: {
          pageTitle: "company",
          breadcrumbs: ["company","company page","create page"],
          greyBg: true,
        },
      },

      {
        path: "/company/JobsandOpportunities/createpages",
        name: "JobsandOpportunitiesCreatePage",
        component: () => import("@/views/Company/CreateJobsandOpportunities.vue"),
        meta: {
          pageTitle: "company",
          breadcrumbs: ["company","Jobs and Opportunities"],
          greyBg: true,
        },
      },
      {
        path: "/subscriptions",
        name: "subscriptions",
        component: () => import("@/views/Subscriptions/Index.vue"),
        meta: {
          pageTitle: "Subscriptions",
          breadcrumbs: ["Subscriptions"],
        },
      },
      {
        path: "/courses/insights/:course_id",
        name: "courses-insights",
        component: () => import("@/views/Courses/Insights.vue"),
        meta: {
          pageTitle: "Insights",
          breadcrumbs: ["Courses", "Insights"],
        },
      },
      {
        path: "/explore/industries",
        name: "explore-industries",
        component: () => import("@/views/Explore/Industries/Index.vue"),
        meta: {
          pageTitle: "Industries",
          breadcrumbs: ["Explore", "Industries"],
        },
      },
      {
        path: "/explore/industry/:id",
        name: "explore-industry-detail",
        props: true,
        component: () => import("@/views/Explore/Industries/Detail.vue"),
        meta: {
          pageTitle: "Industries",
          breadcrumbs: ["Explore", "Industries", "Industry"],
        },
      },
      {
        path: "/exploreindustry/:id/template/:unit",
        name: "explore-industry-template",
        props: true,
        component: () => import("@/views/Explore/Industries/Template.vue"),
        meta: {
          pageTitle: "Industries",
          breadcrumbs: ["Explore", "Industries", "Industry", "Template"],
        },
      },
      {
        path: "/tasks/lessons",
        name: "tasks-lessons-list",
        component: () => import("@/views/Tasks/Lessons/Index.vue"),
        meta: {
          pageTitle: "Lessons",
          breadcrumbs: ["Tasks", "Lessons"],
        },
      },
      {
        path: "/tasks/lessons/:id",
        name: "task-lessons-detail",
        props: true,
        component: () => import("@/views/Tasks/Lessons/Detail.vue"),
        meta: {
          pageTitle: "Lessons",
          breadcrumbs: ["Tasks", "Lessons"],
        },
      },
      {
        path: "/tasks/lessons/:id/sections/:sectionid",
        name: "task-lessons-section-detail",
        props: true,
        component: () => import("@/views/Tasks/Lessons/Section.vue"),
        meta: {
          pageTitle: "Lessons",
          breadcrumbs: ["Tasks", "Lessons"],
        },
      },
      {
        path: "/tasks/lessons/:id/final",
        name: "task-lessons-section-final-step",
        props: true,
        component: () => import("@/views/Tasks/Lessons/Final.vue"),
        meta: {
          pageTitle: "Lessons",
          breadcrumbs: ["Tasks", "Lessons", "Final Step"],
        },
      },
      {
        path: "/tasks/lessons/:id/response",
        name: "task-lessons-view-response",
        props: true,
        component: () => import("@/views/Tasks/Lessons/Response.vue"),
        meta: {
          pageTitle: "Lessons",
          breadcrumbs: ["Tasks", "Lessons", "Response"],
        },
      },
      {
        path: "/tasks/lessons/:id/students/:student",
        name: "task-lessons-view-student-response",
        props: true,
        component: () => import("@/views/Tasks/Lessons/StudentResponse.vue"),
        meta: {
          pageTitle: "Lessons",
          breadcrumbs: ["Tasks", "Lessons", "Response"],
        },
      },
      {
        path: "/tasks/skillstraining",
        name: "tasks-skillstraining-list",
        component: () => import("@/views/Tasks/Skillstraining/Index.vue"),
        meta: {
          pageTitle: "Skills Training",
          breadcrumbs: ["Tasks", "Skills Training"],
        },
      },
      {
        path: "/tasks/skillstraining/:id",
        name: "task-skillstraining-detail",
        props: true,
        component: () => import("@/views/Tasks/Skillstraining/Detail.vue"),
        meta: {
          pageTitle: "Skills Training",
          breadcrumbs: ["Tasks", "Skills Training"],
        },
      },
      {
        path: "/tasks/skillstraining/:id/sections/:sectionid",
        name: "task-skillstraining-section-detail",
        props: true,
        component: () => import("@/views/Tasks/Skillstraining/Section.vue"),
        meta: {
          pageTitle: "Skills Training",
          breadcrumbs: ["Tasks", "Skills Training"],
        },
      },
      {
        path: "/tasks/skillstraining/:id/final",
        name: "task-skillstraining-section-final-step",
        props: true,
        component: () => import("@/views/Tasks/Skillstraining/Final.vue"),
        meta: {
          pageTitle: "Skills Training",
          breadcrumbs: ["Tasks", "Skills Training", "Final Step"],
        },
      },
      {
        path: "/tasks/skillstraining/:id/response",
        name: "task-skillstraining-view-response",
        props: true,
        component: () => import("@/views/Tasks/Skillstraining/Response.vue"),
        meta: {
          pageTitle: "Skills Training",
          breadcrumbs: ["Tasks", "Skills Training", "Response"],
        },
      },
      {
        path: "/tasks/skillstraining/:id/students/:student",
        name: "task-skillstraining-view-student-response",
        props: true,
        component: () => import("@/views/Tasks/Skillstraining/StudentResponse.vue"),
        meta: {
          pageTitle: "Skills Training",
          breadcrumbs: ["Tasks", "Skills Training", "Response"],
        },
      },
      {
        path: "/tasks/vwe",
        name: "tasks-vwe-list",
        component: () => import("@/views/Tasks/Vwe/Index.vue"),
        meta: {
          pageTitle: "Virtual Work Experience",
          breadcrumbs: ["Tasks", "Virtual Work Experience"],
        },
      },
      {
        path: "/tasks/vwe/:id",
        name: "task-vwe-detail",
        props: true,
        component: () => import("@/views/Tasks/Vwe/Detail.vue"),
        meta: {
          pageTitle: "Virtual Work Experience",
          breadcrumbs: ["Tasks", "Virtual Work Experience"],
        },
      },
      {
        path: "/tasks/vwe/:id/sections/:sectionid",
        name: "task-vwe-section-detail",
        props: true,
        component: () => import("@/views/Tasks/Vwe/Section.vue"),
        meta: {
          pageTitle: "Virtual Work Experience",
          breadcrumbs: ["Tasks", "Virtual Work Experience"],
        },
      },
      {
        path: "/tasks/vwe/:id/final",
        name: "task-vwe-section-final-step",
        props: true,
        component: () => import("@/views/Tasks/Vwe/Final.vue"),
        meta: {
          pageTitle: "Virtual Work Experience",
          breadcrumbs: ["Tasks", "Virtual Work Experience", "Final Step"],
        },
      },
      {
        path: "/tasks/vwe/:id/response",
        name: "task-vwe-view-response",
        props: true,
        component: () => import("@/views/Tasks/Vwe/Response.vue"),
        meta: {
          pageTitle: "Virtual Work Experience",
          breadcrumbs: ["Tasks", "Virtual Work Experience", "Response"],
        },
      },
      {
        path: "/tasks/vwe/:id/students/:student",
        name: "task-vwe-view-student-response",
        props: true,
        component: () => import("@/views/Tasks/Vwe/StudentResponse.vue"),
        meta: {
          pageTitle: "Virtual Work Experience",
          breadcrumbs: ["Tasks", "Virtual Work Experience", "Response"],
        },
      },
      {
        path: "/tools/coursefinder",
        name: "course-finder",
        props: true,
        component: () => import("@/views/Tools/CourseFinder/Index.vue"),
        meta: {
          pageTitle: "Course Finder",
          breadcrumbs: ["Tools", "Course Finder"],
        },
      },
      {
        path: "/tools/jobfinder",
        name: "job-finder",
        props: true,
        component: () => import("@/views/Tools/JobFinder/Index.vue"),
        meta: {
          pageTitle: "Job Finder",
          breadcrumbs: ["Tools", "Job Finder"],
        },
      },
      {
        path: "/tools/employerSearch", 
        name: "employer-search",
        component: () => import("@/views/Tools/EmployerSearch/Index.vue"),
        meta: {
          pageTitle: "Employer Search",
          breadcrumbs: ["Tools", "Employer Search"],
          greyBg: true,
        },
      },
      {
        path: "/tools/employerSearch/viewCompany", 
        name: "employer-search-view-company",
        component: () => import("@/views/Tools/EmployerSearch/Company/Index.vue"),
        meta: {
          pageTitle: "Employer Search",
          breadcrumbs: ["Tools", "View Company"],
          greyBg: true,
        },
      },
      {
        path: "/tools/scholarshipsfinder",
        name: "scholarships-finder",
        props: true,
        component: () => import("@/views/Tools/ScholarshipFinder/Index.vue"),
        meta: {
          pageTitle: "Scholarships Finder",
          breadcrumbs: ["Tools", "Scholarships Finder"],
        },
      },

      {
        path: "/pipeline",
        name: "Pipeline",
        props: true,
        component: () => import("@/views/Pipeline/Index.vue"),
        meta: {
          pageTitle: "Pipeline",
          breadcrumbs: ["Pipeline"],
        },
      },
    ],
  },
  {
    path: "/",
    component: () => import("@/layouts/AuthLayout.vue"),
    children: [
      {
        path: "/sign-in",
        name: "sign-in",
        component: () => import("@/views/crafted/authentication/basic-flow/SignIn.vue"),
        meta: {
          pageTitle: "Sign In",
        },
      },
      {
        path: "/slide",
        name: "slide",
        component: () => import("@/views/crafted/authentication/basic-flow/Slide.vue"),
        meta: {
          pageTitle: "Slide",
        },
      },
      {
        path: "/sign-up",
        name: "sign-up",
        component: () => import("@/views/crafted/authentication/MultiStepSignUp.vue"),
        meta: {
          pageTitle: "Sign Up",
        },
      },
      {
        path: "/sign-up/student",
        name: "student-sign-up",
        component: () => import("@/views/crafted/authentication/student/SignUp.vue"),
        meta: {
          pageTitle: "Sign Up",
        },
      },
      {
        path: "/sign-up-univ/student",
        name: "sign-up-univ",
        component: () => import("@/views/crafted/authentication/student/SignUpUniv.vue"),
        meta: {
          pageTitle: "Sign Up Univ",
        },
      },
      {
        path: "/sign-up/addparent",
        name: "add-parent",
        component: () => import("@/views/crafted/authentication/student/AddParent.vue"),
        meta: {
          pageTitle: "Sign Up",
        },
      },

      {
        path: "/sign-up/parent",
        name: "parent-sign-up",
        component: () => import("@/views/crafted/authentication/parent/SignUp.vue"),
        meta: {
          pageTitle: "Sign Up",
        },
      },
      {
        path: "/sign-up/parent/:stage/:invitetoken",
        name: "parent-sign-up-buylicense",
        component: () => import("@/views/crafted/authentication/parent/SignUp.vue"),
        meta: {
          pageTitle: "Sign Up",
        },
      },
      {
        path: "/sign-up/teacher",
        name: "teacher-sign-up",
        component: () => import("@/views/crafted/authentication/teacher/SignUp.vue"),
        meta: {
          pageTitle: "Sign Up",
        },
      },
      {
        path: "/checkauth",
        name: "checkauth",
        component: () => import("@/views/crafted/authentication/student/StripeIndividualSuccess.vue"),
        meta: {
          pageTitle: "checkauth",
        },
      },
      {
        path: "/password-reset",
        name: "password-reset",
        component: () => import("@/views/crafted/authentication/basic-flow/PasswordReset.vue"),
        meta: {
          pageTitle: "Password reset",
        },
      },
      {
        path: "/reset-password/:token",
        name: "reset-password",
        component: () => import("@/views/crafted/authentication/basic-flow/ResetPassword.vue"),
        meta: {
          pageTitle: "Password reset",
        },
      },
    ],
  },
  {
    path: "/",
    component: () => {
      // Check auth status from store
      const isAuthenticated = store.getters.isUserAuthenticated;
      return isAuthenticated
        ? import("@/layouts/main-layout/MainLayout.vue") // Private view
        : import("@/layouts/PublicLayout.vue"); // Public view
    },
    children: [
      {
        // the 404 route, when none of the above matches
        path: "/badges/check-credentials",
        name: "check-credentials",
        component: () => import("@/views/Tasks/Badges/CheckCredentails.vue"),
        meta: {
          pageTitle: "Verify Credentail",
        },
      },
    ],
  },
  {
    path: "/",
    component: () => import("@/layouts/SystemLayout.vue"),
    children: [
      {
        // the 404 route, when none of the above matches
        path: "/404",
        name: "404",
        component: () => import("@/views/crafted/authentication/Error404.vue"),
        meta: {
          pageTitle: "Error 404",
        },
      },
      {
        path: "/500",
        name: "500",
        component: () => import("@/views/crafted/authentication/Error500.vue"),
        meta: {
          pageTitle: "Error 500",
        },
      },
    ],
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
var getParents = function (elem, selector) {
  var barElement: HTMLElement | null = elem;
  if (barElement) {
    barElement.style.removeProperty("height");
    barElement.style.removeProperty("position");
  }
  while (barElement && !barElement.classList.contains(selector)) {
    barElement = barElement.parentElement;
    if (barElement) {
      barElement.style.removeProperty("height");
      barElement.style.removeProperty("position");
    }
  }

  var parents = [];

  return parents;
};

router.beforeEach(async (to, from, next) => {
  // current page view title
  document.title = `${to.meta.pageTitle} - ${import.meta.env.VITE_APP_NAME}`;
  var breadcrumbs = to.meta.breadcrumbs ? to.meta.breadcrumbs : [];
  // if(typeof breadcrumbs =='object' && to.name=="task-lessons-detail" ){

  //   // breadcrumbs=["Tasks", "Lessons"];
  //   var k=Object.keys(breadcrumbs).length;
  //   ApiService.get("api/lessonname/"+to.params.id)
  //     .then(({ data }) => {
  //       breadcrumbs[2]=data;
  //     })
  //     .catch(({ response }) => {
  //       console.log(response);
  //     });
  // }

  // if(typeof breadcrumbs =='object' && to.name=="task-lessons-section-detail" ){
  //   // breadcrumbs=["Tasks", "Lessons"];
  //   var k=Object.keys(breadcrumbs).length;
  //   ApiService.get("api/sectionwithlessonname/"+to.params.id+"/"+to.params.sectionid)
  //     .then(({ data }) => {
  //       breadcrumbs[2]=data.lesson;
  //       breadcrumbs[3]=data.section;
  //     })
  //     .catch(({ response }) => {
  //       console.log(response);
  //     });
  // }

  // reset config to initial state
  store.commit(Mutations.RESET_LAYOUT_CONFIG);
  store.commit(Mutations.SET_BREADCRUMB_MUTATION, breadcrumbs);
  /* This code removes the height and positon css properties from all parent elements that fullpagejs has added on dashboard */
  if (typeof from.name != "undefined" && from.name == "dashboard") {
    var elem = document.querySelector("#kt_app_content_container");
    getParents(elem, "div");
  }
  // verify auth token before each page change
  await store.dispatch(Actions.VERIFY_AUTH, { api_token: JwtService.getToken() });

  const isAuthenticated = store.getters.isUserAuthenticated;
  const lasturl = store.getters.lastAccessedUrl;
  const isAdmin = store.getters.currentUser.isAdmin;
  const isVueRedirect = store.getters.hasVueRedirect;
  const intendedParams = store.getters.intendedParamValues;

  if (Array.isArray(to.meta.middleware)) {
    for (const middleware of to.meta.middleware) {
      if (typeof middleware === "string" && middleware === "auth") {
        if (!isAuthenticated) {
          // console.log('User not authenticated, saving intended URL and redirecting to checkauth.');
          // Save the intended URL and parameters
          await store.dispatch(Actions.SET_INTENDED_URL, { url: to.name, params: to.params });
          next({ name: "checkauth" });
          return;
        } else {
          // If the user is authenticated, check if there's a last URL to redirect to
          if (lasturl && lasturl !== "dashboard") {
            await store.dispatch(Actions.RESET_INTENDED_URL, { api_token: JwtService.getToken() });

            if (!isVueRedirect) {
              if (to.fullPath === lasturl) {
                // console.log('Already on the target route, skipping navigation.');
                next(); // Avoid loop
              } else {
                // console.log('Redirecting to lasturl:', lasturl);
                window.location.href = lasturl; // Directly redirect
              }
            } else {
              if (to.name === lasturl && JSON.stringify(to.params) === JSON.stringify(intendedParams)) {
                // console.log('Already on the Vue Redirect target, skipping navigation.');
                next();
              } else {
                // console.log('Redirecting via Vue Redirect:', { name: lasturl, params: intendedParams });
                next({ name: lasturl, params: intendedParams });
              }
            }
          } else {
            // If no lasturl, continue with the normal navigation
            // console.log('No specific redirect logic, continuing navigation.');
            if (isAdmin) {
              window.location.href = "/";
              return;
            }
            next();
          }
        }
        // return;
      } else if (typeof middleware === "string" && middleware === "hasNewGamePlan") {
        // console.log("newGameplan", store.getters.userHasNewGamePlan);
        if (!isAuthenticated || !store.getters.userHasNewGamePlan) {
          if (to.name !== "gameplan") {
            // console.log("Redirecting to gameplan");
            router.replace({ name: "gameplan" });
          }
          //  else {
          //     // console.log("Already on gameplan route, stopping redirect");
          // }
        }
      } else if (typeof middleware === "string" && middleware === "hasCampus") {
        if (!store.getters.userHasCampus) {
          // console.log("campus", store.getters.userHasCampus);
          window.location.href = "/profiles/edit";
          return;
        }
      }
    }

    // console.log("lasturl:", lasturl);
    // console.log("isVueRedirect:", isVueRedirect);
    // console.log("intendedParams:", intendedParams);
  } else {
    next();
  }

  // before page access check if page requires authentication
  // if (to.meta.middleware === "auth") {
  //     const isAuthenticated = store.getters.isUserAuthenticated;
  //     const lasturl = store.getters.lastAccessedUrl;
  //     const isVueRedirect = store.getters.hasVueRedirect;
  //     const intendedParams = store.getters.intendedParamValues;

  //     console.log("userHasCampus", store.getters.userHasCampus);
  //     console.log("userHasParentActiveSubscription", store.getters.userHasParentActiveSubscription);

  //     console.log('Navigation Guard Debug:', {
  //         lasturl,
  //         isVueRedirect,
  //         intendedParams,
  //         isAuthenticated
  //     });

  //     if (isAuthenticated) {

  //         if (lasturl && lasturl !== 'dashboard') {
  //             await store.dispatch(Actions.RESET_INTENDED_URL, { api_token: JwtService.getToken() });

  //             if (!isVueRedirect) {
  //                 if (to.fullPath === lasturl) {
  //                     console.log('Already on the target route, skipping navigation.');
  //                     next(); // Avoid loop
  //                 } else {
  //                     console.log('Redirecting to lasturl:', lasturl);
  //                     window.location.href = lasturl;
  //                     // next({ path: lasturl }); // Redirect to `lasturl`
  //                 }
  //             } else {
  //                 if (to.name === lasturl && JSON.stringify(to.params) === JSON.stringify(intendedParams)) {
  //                     console.log('Already on the Vue Redirect target, skipping navigation.');
  //                     next();
  //                 } else {
  //                     console.log('Redirecting via Vue Redirect:', { name: lasturl, params: intendedParams });
  //                     next({ name: lasturl, params: intendedParams });
  //                 }
  //             }
  //         } else {
  //             if (store.getters.userHasGamePlanAccess && !store.getters.userHasPlan && store.getters.planRedirectUrl) {
  //                 if (to.name === 'gameplan') {
  //                     console.log('Already at Game Plan page, skipping navigation.');
  //                     next();
  //                 } else {
  //                     console.log('Redirecting to Game Plan page');
  //                     next({ name: "gameplan" });
  //                 }
  //             }
  //             // else if (!store.getters.userHasCampus) {
  //             //     console.log("not has campus")
  //             // }
  //             // else if (!store.getters.userHasParentActiveSubscription) {
  //             //     console.log("not has parentActiveSubscription");
  //             // }
  //             else if (!store.getters.currentUser.isStudent &&
  //                 !store.getters.currentUser.isParent &&
  //                 !store.getters.currentUser.isTeacher) {
  //                 if (to.path === '/home') {
  //                     console.log('Already at home, skipping navigation.');
  //                     next();
  //                 } else {
  //                     console.log('Redirecting to home');
  //                     next({ path: '/home' });
  //                 }
  //             } else {
  //                 console.log('No specific redirect logic, continuing navigation.');
  //                 next();
  //             }
  //         }
  //     } else {
  //         console.log('User not authenticated, saving intended URL and redirecting to checkauth.');
  //         await store.dispatch(Actions.SET_INTENDED_URL, { url: to.name, params: to.params });
  //         next({ name: "checkauth" });
  //     }
  // } else {
  //     console.log('No middleware, continuing navigation.');
  //     next();
  // }

  // Scroll page to top on every route change
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: "smooth",
  });
});

export default router;
