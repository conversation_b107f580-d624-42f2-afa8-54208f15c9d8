<template>
    <div class="modal fade" id="kt_modal_badge" tabindex="-1" style="display: none" aria-hidden="true">
        <div :class="['modal-dialog modal-dialog-centered', isFullscreen ? 'custom-fullscreen-modal' : 'mw-1200px']">
            <div class="modal-content rounded-0">
                <div class="modal-header text-white">
                    <h5 class="modal-title">View Badge</h5>
                    <div>
                        <!-- <span class="mx-4 cursor-pointer" @click="toggleFullscreen">
                            <i v-if="isFullscreen" class="fa-solid fa-compress text-black"></i>
                            <i v-else class="fa-solid fa-expand text-black"></i>
                        </span> -->
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body text-center px-10">
                    <div class="row gap-4 fs-5">
                        <div class="col-7 px-7 py-9 text-start border border-solid rounded">
                            <div>
                                <h1>{{ selectedBadge?.badge?.name }}</h1>
                                <p class="fw-bold mb-5 mt-5">
                                    Verified by
                                    <span v-for="(company, index) in selectedBadge?.badge?.companies" :key="company.id">
                                        <u>{{ company.name }}</u>
                                        <span v-if="index !== selectedBadge?.badge?.companies.length - 1"> + </span>
                                    </span>
                                </p>
                            </div>
                            <div class="mt-7 lh-lg">
                                <p class="mb-1"><span class="text-gray-700">Module Name: </span>{{
                                    selectedBadge?.module_name }}</p>
                                <!-- <p class=" mb-1">
                                    <span class="text-gray-700">Credential ID: </span>
                                    {{ selectedBadge?.credential_id || "N/A" }}
                                </p>
                                <p class=" mb-1"><span class="text-gray-700">Issue Date: </span> {{ selectedBadge?.issue_date }}</p>
                                <p v-if="selectedBadge?.expiration_date" class=" mb-1">
                                    <span class="text-gray-700">Expiry Date: </span> {{ selectedBadge.expiration_date }}
                                </p> -->
                                <p class=" mb-1"><span class="text-gray-700">Module Type: </span>{{
                                    selectedBadge?.module_type }}</p>
                            </div>
                        </div>
                        <div class="col my-auto">
                            <div v-if="selectedBadge">
                                <div class="animated-video" v-if="selectedBadge?.badge?.video"
                                    v-html="selectedBadge?.badge?.video"></div>
                                <img v-else
                                    :src="selectedBadge?.badge?.animated_image_fullpath || selectedBadge?.badge?.image_fullpath"
                                    alt="Animated Badge" class="w-100">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import * as bootstrap from "bootstrap";

interface BadgeCompany {
    id: number;
    name: string;
}

interface Badge {
    name: string;
    companies: BadgeCompany[];
    video?: string;
    animated_image_fullpath?: string;
    image_fullpath?: string;
}

interface SelectedBadge {
    badge?: Badge;
    module_name?: string;
    module_type?: string;
}

const props = defineProps<{
    selectedBadge?: SelectedBadge;
}>();

const emit = defineEmits<{
    (e: "openShareModal", badge?: SelectedBadge): void;
}>();

const isFullscreen = ref(false);

function toggleFullscreen() {
    isFullscreen.value = !isFullscreen.value;
 }

function isVideo(filePath: string) {
    return filePath && filePath.endsWith(".mp4");
}

function openShareModal() {
    const currentModal = document.getElementById("kt_modal_badge");
    if (currentModal) {
        const bsCurrentModal = bootstrap.Modal.getInstance(currentModal);
        if (bsCurrentModal) {
            bsCurrentModal.hide();
        }
    }
    emit("openShareModal", props.selectedBadge);
}
</script>

<style>
.mw-900px {
    max-width: 900px;
}

.mw-1200px {
    max-width: 1200px;
}

.animated-video>iframe {
    width: 100% !important;
    height: 100% !important;
}

.custom-fullscreen-modal {
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0;
}
</style>